package com.desaysv.workserver.algorithm.video;

import com.desaysv.workserver.algorithm.ocr.OcrMatching;
import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.utils.StructuralSimilarityUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.*;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Rect;
import org.springframework.beans.factory.annotation.Autowired;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
public class AlarmLightBlinkingDetector {

    private final Queue<Frame> frameQueue;
    private final Rect rect;//区域
    private final double frameRate;//帧率
    @Getter
    private double frequency;//频率
    //实际采集图像
    @Getter
    @Setter
    private String captureImagePath;
    //模板采集图像
    @Getter
    @Setter
    private String templateImagePath;
    //实际ROI图像
    @Getter
    @Setter
    private String captureRoiPath;
    @Setter
    private double targetSimilarity;
    private final double threshold;

    public AlarmLightBlinkingDetector() {
        this.frameQueue = new LinkedBlockingQueue<>();
        this.rect = new Rect(0, 0, 0, 0);
        this.frameRate = 30;
        this.targetSimilarity = 0.80;
        this.threshold = 0.55;
    }

    /**
     * 构造函数
     *
     * @param frameQueue       帧队列
     * @param rect             roi
     * @param frameRate        帧率
     * @param threshold        不亮与亮之间的相似度阈值
     * @param targetSimilarity 模板图像与目标图像的相似度
     */
    public AlarmLightBlinkingDetector(Queue<Frame> frameQueue, Rect rect, double frameRate, double threshold, double targetSimilarity) {
        this.frameQueue = frameQueue;
        this.rect = rect;
        this.frameRate = frameRate;
        this.targetSimilarity = targetSimilarity;
        this.threshold = threshold;
    }

    public boolean detect(Frame templateFrame) {
        double minSimilarity = Double.MAX_VALUE;
        double secondMinSimilarity = Double.MAX_VALUE;
        frequency = 0;
        int i = 0;
        int halfPeriod = 0;
        int[] halfPeriodNumber = new int[100];
        long[] periodTimes = new long[100];
        periodTimes[0] = 0;
        List<Mat> croppedJList = new ArrayList<>();

        //模板帧
        Mat croppedI = Java2DFrameUtils.toMat(templateFrame);
        halfPeriodNumber[0] = 1;
        Frame frame1 = frameQueue.poll();
        if (frame1 == null) {
            return false;
        }
        // 逐帧比较，确认闪烁
        while (!frameQueue.isEmpty()) {
            Frame frame = frameQueue.poll();
            if (frame == null) break;
            i++;

            // 转换为 Mat
            Mat matJ = Java2DFrameUtils.toMat(frame);
            Mat croppedJ = new Mat(matJ, this.rect);
            croppedJList.add(croppedJ);

            // 逐帧相似度比较
            double similarity = StructuralSimilarityUtils.getSSIM(croppedI, croppedJ);
            log.info("第{}帧与模板对比相似度: {}", i, similarity);

            // 更新最小值和第二最小值
            if (similarity < minSimilarity) {
                // 如果当前相似度比最小值还小，则更新第二最小值为之前的最小值
                secondMinSimilarity = minSimilarity;
                minSimilarity = similarity;
            } else if (similarity < secondMinSimilarity) {
                // 如果当前相似度介于最小值和第二最小值之间，则更新第二最小值
                secondMinSimilarity = similarity;
            }

            // 如果相似度低于对比阈值，表示检测到亮度变化，进入下一个半周期
            if (similarity < threshold) { //逐帧对比阈值应该动态调整
                halfPeriod++;
                halfPeriodNumber[halfPeriod] = 0;
                periodTimes[halfPeriod] = 0;
                croppedI = croppedJ; // 更新当前帧为基准帧
                if (halfPeriod >= 2) {
                    periodTimes[halfPeriod] = (long) ((halfPeriodNumber[halfPeriod - 1] +
                            halfPeriodNumber[halfPeriod - 2]) * (1000 / frameRate));
                }
            }
            halfPeriodNumber[halfPeriod]++;
        }
        int countTimes = 0;
        for (int t = 0; t <= halfPeriod; t++) {
            if (t >= 3) {
                countTimes += (int) periodTimes[t];
            }
        }

        if (countTimes == 0) {
            log.info("未检测到闪烁。与模板帧ssim对比最小相似度: {}, 不满足亮度阈值：{}", secondMinSimilarity, threshold);
            handleFailedDetection(croppedI, croppedJList);
            return false;
        }

        frequency = (halfPeriod - 2) / (countTimes / 1000.0);
        log.info("确认闪烁 帧率{} 最后频率为：{}", frameRate, frequency);
        // 检测到闪烁后，使用模板帧进行相似度比较
        if (templateFrame != null) {
            if (targetSimilarity == 0.0) {
                log.info("不判断相似度比例");
                return true;
            }
            int j = 0;
            for (Mat croppedJ : croppedJList) {
                j++;
                double similarity = StructuralSimilarityUtils.getSSIM(croppedI, croppedJ);
                if (similarity >= targetSimilarity) {
                    log.info("第{}张与模板相似度为：{} 复合目标值{}", j, similarity, targetSimilarity);
                    return true;
                } else {
                    log.info("第{}张与模板相似度为：{} 不复合目标值{}", j, similarity, targetSimilarity);
                }
            }
            return false;
        }
        return true;
    }

    public boolean detectAndOcrMatch(Frame templateFrame, OcrMatching ocrMatching, String matchedText) {
        double minSimilarity = Double.MAX_VALUE;
        double secondMinSimilarity = Double.MAX_VALUE;
        frequency = 0;
        int i = 0;
        int halfPeriod = 0;
        int[] halfPeriodNumber = new int[100];
        long[] periodTimes = new long[100];
        periodTimes[0] = 0;
        List<Mat> croppedJList = new ArrayList<>();

        //模板帧
        Mat croppedI = Java2DFrameUtils.toMat(templateFrame);
        halfPeriodNumber[0] = 1;
        Frame frame1 = frameQueue.poll();
        if (frame1 == null) {
            return false;
        }
        // 逐帧比较，确认闪烁
        while (!frameQueue.isEmpty()) {
            Frame frame = frameQueue.poll();
            if (frame == null) break;
            i++;

            // 转换为 Mat
            Mat matJ = Java2DFrameUtils.toMat(frame);
            Mat croppedJ = new Mat(matJ, this.rect);
            croppedJList.add(croppedJ);

            // 逐帧相似度比较
            double similarity = StructuralSimilarityUtils.getSSIM(croppedI, croppedJ);
            log.info("第{}帧与模板对比相似度: {}", i, similarity);

            // 更新最小值和第二最小值
            if (similarity < minSimilarity) {
                // 如果当前相似度比最小值还小，则更新第二最小值为之前的最小值
                secondMinSimilarity = minSimilarity;
                minSimilarity = similarity;
            } else if (similarity < secondMinSimilarity) {
                // 如果当前相似度介于最小值和第二最小值之间，则更新第二最小值
                secondMinSimilarity = similarity;
            }

            // 如果相似度低于对比阈值，表示检测到亮度变化，进入下一个半周期
            if (similarity < threshold) { //逐帧对比阈值应该动态调整
                halfPeriod++;
                halfPeriodNumber[halfPeriod] = 0;
                periodTimes[halfPeriod] = 0;
                croppedI = croppedJ; // 更新当前帧为基准帧
                if (halfPeriod >= 2) {
                    periodTimes[halfPeriod] = (long) ((halfPeriodNumber[halfPeriod - 1] +
                            halfPeriodNumber[halfPeriod - 2]) * (1000 / frameRate));
                }
            }
            halfPeriodNumber[halfPeriod]++;
        }
        int countTimes = 0;
        for (int t = 0; t <= halfPeriod; t++) {
            if (t >= 3) {
                countTimes += (int) periodTimes[t];
            }
        }

        if (countTimes == 0) {
            log.info("未检测到闪烁。与模板帧ssim对比最小相似度: {}, 不满足亮度阈值：{}", secondMinSimilarity, threshold);
            handleFailedDetection(croppedI, croppedJList);
            return false;
        }

        frequency = (halfPeriod - 2) / (countTimes / 1000.0);
        log.info("确认闪烁 帧率{} 最后频率为：{}", frameRate, frequency);
        // 检测到闪烁后，使用模板帧进行相似度比较
        if (templateFrame != null) {
            if (targetSimilarity == 0.0) {
                log.info("不判断相似度比例");
                return true;
            }
            int j = 0;
            for (Mat croppedJ : croppedJList) {
                j++;
                double similarity = StructuralSimilarityUtils.getSSIM(croppedI, croppedJ);
                if (similarity >= targetSimilarity) {
                    log.info("第{}张与模板相似度为：{} 复合目标值{}", j, similarity, targetSimilarity);
                    double ocrSimilarity = ocrMatching.templateMatchingWithFrame(Java2DFrameUtils.toFrame(croppedJ), matchedText).getScore();
                    if (ocrSimilarity >= targetSimilarity){
                        log.info("第{}张ocr文字识别的得分为：{} 复合目标值{}", j, ocrSimilarity, targetSimilarity);
                        return true;}
                    else {
                        log.info("第{}张ocr文字识别的得分为：{} 不复合目标值{}", j, ocrSimilarity, targetSimilarity);
                    }
                } else {
                    log.info("第{}张与模板相似度为：{} 不复合目标值{}", j, similarity, targetSimilarity);
                }
            }
            return false;
        }
        return true;
    }

    private void handleFailedDetection(Mat captureMat, List<Mat> croppedRoiList) {
        if (!SystemEnv.isSaveFailImage()) {
            return;
        }

        try {
            String captureImageFile = "\\模板图.png";
            saveMatToFile(captureMat, templateImagePath + captureImageFile);

            // 删除对比图详情文件夹及内容
            File compareImageDirectory = new File(captureRoiPath + "\\对比图详情");
            if (compareImageDirectory.exists()) {
                deleteDirectory(compareImageDirectory);
            }
            for (int i = 0; i < croppedRoiList.size(); i++) {
                // 保存实际 ROI 图像
                String captureRoiFile = "\\对比ROI_" + i + ".png";
                saveMatToFile(croppedRoiList.get(i), captureRoiPath + "\\对比图详情" + captureRoiFile);
            }

            // 记录日志信息
            log.info("保存失败图像: 实际采集图像路径: {}, ROI 图像路径: {}", captureImagePath + captureImageFile, captureRoiPath + "\\对比图详情");
        } catch (Exception e) {
            log.error("保存失败图像时出错: {}", e.getMessage());
        }
    }

    public static void saveMatToFile(Mat mat, String filePath) {
        BufferedImage image = Java2DFrameUtils.toBufferedImage(mat);

        // 保存 BufferedImage 到文件
        File outputFile = new File(filePath);
        if (!outputFile.getParentFile().exists()) {
            outputFile.getParentFile().mkdirs();
        }

        try {
            ImageIO.write(image, "png", outputFile);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }


    public static void main(String[] args) throws FrameGrabber.Exception {
        // 视频源，可以是摄像头或视频文件
        FrameGrabber grabber = new OpenCVFrameGrabber(0); // 打开默认摄像头
        grabber.start();

        // 定义检测区域和录制时间
        Rect detectionRect = new Rect(441, 252, 30, 22);
        //Rect detectionRect = new Rect(49, 219, 14, 13); // 假设报警灯位于 (100, 100) 且大小为 50x50 的区域
        long recordingTime = 5000; // 录制时间，单位为毫秒

        // 帧队列
        Queue<Frame> frameQueue = new LinkedBlockingQueue<>();

        // 创建报警灯闪烁检测器
        double frameRate = grabber.getFrameRate();
        System.out.println("frameRate:" + frameRate);
        AlarmLightBlinkingDetector detector = new AlarmLightBlinkingDetector(frameQueue, detectionRect, frameRate, 0.55, 0.8);

        CanvasFrame canvas = new CanvasFrame("Webcam");
        canvas.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < recordingTime && canvas.isVisible()) {
            Frame frame = grabber.grab();
            if (frame != null) {
                frameQueue.add(frame.clone());
                canvas.showImage(frame);
            }
//            long delay = Math.min(100, (long) (1000 / frameRate)); // 根据帧率计算延时
//            try {
//                Thread.sleep(delay);
////                System.out.println("delay:" + delay);
//            } catch (InterruptedException e) {
//                log.error(e.getMessage(), e);
//            }
        }

        grabber.stop();
        canvas.dispose();

        // 调用检测器进行检测
        boolean result = detector.detect(frameQueue.poll());
        System.out.println("Detection result: " + (result ? "Blinking Detected" : "No Blinking Detected"));
    }

    private void deleteDirectory(File directoryToBeDeleted) {
        File[] allContents = directoryToBeDeleted.listFiles();
        if (allContents != null) {
            for (File file : allContents) {
                deleteDirectory(file);
            }
        }
        directoryToBeDeleted.delete();
    }
}
