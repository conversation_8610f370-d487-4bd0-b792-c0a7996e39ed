package com.desaysv.workserver.finder;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.common.utils.library.SetupApiToolkit;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.devices.usbswtich.UsbSwitchDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.fazecast.jSerialComm.SerialPort;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * USB串口端口查找
 */
@Component
//@CacheConfig(cacheNames = {"deviceFinder"})
public class SerialPortDeviceFinder extends BaseDeviceFinder {

    private static final String[] forbiddenHardwareName = {
            "蓝牙链接上的标准串行", "通讯端口", "通信端口"
    };

    /**
     * 查找USB物理端口
     *
     * @param deviceModel 设备型号
     * @param isLock      是否锁定
     * @return
     */
    public static List<Device> findPhysicalSerialPorts(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        List<SerialPort> portList = SerialUtils.findPorts();
        int deviceIndex;
        PortDevice device;
        for (SerialPort serialPort : portList) {
            if (DeviceModel.Utils.contains(DeviceModel.UsbSwitch.class, deviceModel)) {
                device = new UsbSwitchDevice();
            } else {
                device = new SerialPortDevice();
            }
            device.setDevicePort(Integer.valueOf(Pattern.compile("\\D").matcher(serialPort.getSystemPortName()).replaceAll("")));
            device.setDeviceName(serialPort.getSystemPortName());
            Optional<SetupApiToolkit.WindowsDevice> windowsDevice = SetupApiToolkit.getDeviceByName(serialPort.getSystemPortName());
            if (windowsDevice.isPresent()) {
                SetupApiToolkit.WindowsDevice wd = windowsDevice.get();
                if (isForbidden(wd.getFriendName(), forbiddenHardwareName)) {
                    continue;
                }
                device.setDeviceUniqueCode(wd.getInstanceId());
            } else {
                device.setDeviceUniqueCode(device.getDeviceName());
            }
            deviceIndex = Device.getDeviceModelIndex(deviceModel);
            device.setAliasName(deviceModel + "#" + deviceIndex);
            //FIXME：由客户端指定设备顺序，去掉服务端加#号逻辑
//            device.setDeviceAliasName(deviceModel + "#" + deviceIndex);
            device.setDeviceOperationParameter(new DeviceOperationParameter());
            devices.add(device);
        }
        //去除锁定端口
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
        }
        return devices;
    }

    public static void main(String[] args) {
        SerialPortDeviceFinder.findPhysicalSerialPorts(DeviceModel.Serial.PORT_SERIAL, false);
    }

}
