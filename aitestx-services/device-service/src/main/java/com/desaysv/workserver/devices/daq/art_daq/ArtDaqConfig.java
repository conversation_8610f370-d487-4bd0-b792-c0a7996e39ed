package com.desaysv.workserver.devices.daq.art_daq;

import lombok.Data;

@Data
public class ArtDaqConfig {
    private double minVal = -10.0;
    private double maxVal = 10.0;
    private int terminalConfig = ArtDaq.ArtDAQ_Val_Diff; // 差分模式
    private double sampleRate = 50000.0; // 50kHz采样率
    private int bufferSize = 50000; // 1秒缓冲区
    private double triggerLevel = 1.0;
    
    public ArtDaqConfig() {}
    
    public ArtDaqConfig(double minVal, double maxVal, int terminalConfig, double sampleRate) {
        this.minVal = minVal;
        this.maxVal = maxVal;
        this.terminalConfig = terminalConfig;
        this.sampleRate = sampleRate;
        this.bufferSize = (int) sampleRate; // 默认1秒缓冲区
    }
}