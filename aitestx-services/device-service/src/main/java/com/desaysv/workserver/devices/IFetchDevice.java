package com.desaysv.workserver.devices;

import com.desaysv.workserver.devices.daq.art_daq.ArtDaqApi;
import com.desaysv.workserver.exceptions.device.DeviceReadException;

import java.util.List;

public interface IFetchDevice {

    /**
     * 获取电压
     *
     * @return 电压
     */
//    @JsonIgnore
    float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException;

    default float fetchVoltage(Integer deviceChannel) throws DeviceReadException {
        return fetchVoltage(deviceChannel, null);
    }

    default float fetchVoltage() throws DeviceReadException {
        return fetchVoltage(null, null);
    }

    /**
     * 获取电流
     *
     * @return 电流
     */
//    @JsonIgnore
    float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException;

    default float fetchCurrent(Integer deviceChannel) throws DeviceReadException {
        return fetchCurrent(deviceChannel, null);
    }

    default float fetchCurrent() throws DeviceReadException {
        return fetchCurrent(null, null);
    }

    List<Double> fetchSoundData(Integer deviceChannel, double durationSeconds) throws ArtDaqApi.ArtDaqError;

    default List<Double> fetchSoundData(double durationSeconds) throws ArtDaqApi.ArtDaqError {
        return fetchSoundData(null, durationSeconds);
    }

}
