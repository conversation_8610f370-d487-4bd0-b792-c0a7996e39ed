package com.desaysv.workserver.devices.power;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.daq.art_daq.ArtDaqApi;
import com.desaysv.workserver.devices.power.base.*;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.entity.BooleanComparator;
import com.desaysv.workserver.exceptions.device.*;
import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.monitor.MeasureIndicator;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fazecast.jSerialComm.SerialPortDataListener;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.desaysv.workserver.constants.AppConstants.RANDOM_VALUE_SEPARATOR;

/**
 * 默认电源实现
 */
@Slf4j
@JsonIgnoreProperties({"voltage", "current"})
public abstract class DefaultPower extends PowerDevice implements TestProcessListener {

    //通讯协议
    @Setter
    private String commProtocol = CommProtocol.RS232;

    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @Getter
    @JSONField(serialize = false)
    private JVisaInstrument instrument;

    @JSONField(serialize = false)
    private DeviceDataDispatcher<PowerData> deviceDataDispatcher;

    private SerialPortDataListener serialPortDataListener;

    private boolean testTerminated;

    public DefaultPower() {
        this(new DeviceOperationParameter());
    }

    public DefaultPower(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        testTerminated = false;
    }

    @Override
    public String sendAndReceiveString(String command) throws DeviceSendException {
        if (instrument != null) {
            try {
                return instrument.queryString(command);
            } catch (JVisaException e) {
                throw new DeviceSendException(e);
            }
        }
        throw new DeviceSendException("VISA仪器未连接");
    }

    private boolean openVisaPort() throws DeviceOpenException {
        try {
            if (visaResourceManager == null) {
                visaResourceManager = new JVisaResourceManager();
            }
            instrument = visaResourceManager.openInstrument(getDeviceName());
            // 设置更长的通信超时时间
            instrument.setTimeout(10000);
//             设置写入终止符
            instrument.setWriteTerminator("\n");
//             设置读取终止符
            instrument.setReadTerminationCharacter('\n');
            instrument.setReadTerminationCharacterEnabled(true);
            return true;
        } catch (JVisaException e) {
            visaResourceManager = null;
            throw new DeviceOpenException(e);
        }
    }

    private boolean closeVisaPort() throws JVisaException {
        try {
            if (instrument != null) {
                try {
                    instrument.close();
                } finally {
                    instrument = null;
                }
            }
        } finally {
            if (visaResourceManager != null) {
                try {
                    visaResourceManager.close();
                } finally {
                    visaResourceManager = null;
                }
            }
        }
        return true;
    }

    private boolean openDevice() throws DeviceOpenRepeatException, DeviceOpenException {
        if (commProtocol.equalsIgnoreCase(CommProtocol.USB)) {
            return openVisaPort();
        }
        return super.open();
    }

    public void addDataListener() throws DeviceDataListenerException {
        if (commProtocol.equalsIgnoreCase(CommProtocol.USB)) {
            return;
        }

        if (deviceDataDispatcher == null) {
            deviceDataDispatcher = new DeviceDataDispatcher<>();
        }
        if (serialPortDataListener == null) {
            serialPortDataListener = new PowerPortEventListener(
                    this,
                    deviceDataDispatcher,
                    getPowerProtocol().getDataLength());
            try {
                SerialUtils.addListener(getSerialPort(), serialPortDataListener);
            } catch (SerialExceptions.TooManyListeners | SerialExceptions.UnsupportedCommOperationException e) {
                throw new DeviceDataListenerException(e);
            }
        }
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException {
        if (isSimulated()) {
            return true;
        }
        boolean isOpened;
        try {
            isOpened = openDevice();
        } catch (DeviceOpenRepeatException e) {
            return true;
        }

        if (isOpened) {
            try {
                addDataListener();
            } catch (DeviceDataListenerException e) {
                log.error(e.getMessage(), e);
                isOpened = false;
            }
        }
        return isOpened;
    }

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isSimulated()) {
            return;
        }
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(this);
            log.info("{}设置远程操作模式", getDeviceName());
            getPowerProtocol().setRemoteMode(true);
        }
    }

    private boolean closeDevice() throws DeviceCloseException {
        if (commProtocol.equalsIgnoreCase(CommProtocol.USB)) {
            try {
                return closeVisaPort();
            } catch (JVisaException e) {
                throw new DeviceCloseException(e);
            }
        }
        return super.close();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        log.info("{}设置面板操作模式", getDeviceName());
        //FIXME：下面一条，报掉后执行耗时太久
        getPowerProtocol().setRemoteMode(false);
        boolean isClosed = closeDevice();
        if (deviceDataDispatcher != null) {
            deviceDataDispatcher.stopAll();
        }
        deviceDataDispatcher = null;
        serialPortDataListener = null;
        return isClosed;
    }

    @Override
    public boolean send(String message) throws DeviceSendException {
        if (commProtocol.equalsIgnoreCase(CommProtocol.USB)) {
            if (instrument == null) {
                throw new DeviceSendException("Visa仪器未连接");
            }
            try {
                instrument.write(message);
                return true;
            } catch (JVisaException e) {
                throw new DeviceSendException(e);
            }
        }
        return super.send(message);
    }

    @Override
    public boolean fetchOutput() {
        return getPowerProtocol().fetchOutput();
    }

    @Override
    public List<Double> fetchSoundData(Integer deviceChannel, double durationSeconds) throws ArtDaqApi.ArtDaqError {
        return Collections.emptyList();
    }

    @Override
    public boolean outputOn(Integer deviceChannel) {
        if (deviceChannel == null) {
            log.info("{}输出打开", getDeviceName());
        } else {
            log.info("{}通道{}输出打开", getDeviceName(), deviceChannel);
        }
        if (isSimulated()) {
            return true;
        }
        if (getPowerProtocol().setOutput(deviceChannel, true)){
            System.out.println("输出打开成功,模式为：" + getPowerProtocol().getClass().getName());
            if (!getPowerProtocol().getClass().getName().contains("PowerControlBoxProtocol")){
                try {
                    float current = fetchCurrent(null,null);
                    log.info("{}通道{}输出打开成功,电流为{}A", getDeviceName(), deviceChannel, current);
                    return true;
                }
                catch (DeviceReadException e){
                    log.info("{}通道{}输出打开失败", getDeviceName(), deviceChannel);
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean outputOff(Integer deviceChannel) {
        if (deviceChannel == null) {
            log.info("{}输出关闭", getDeviceName());
        } else {
            log.info("{}通道{}输出关闭", getDeviceName(), deviceChannel);
        }
        if (isSimulated()) {
            return true;
        }
        //获取电流验证
        if (getPowerProtocol().setOutput(deviceChannel, false)){
            System.out.println("输出关闭成功,模式为：" + getPowerProtocol().getClass().getName());
            if (!getPowerProtocol().getClass().getName().contains("PowerControlBoxProtocol")){
                try {
                    float  current = fetchCurrent(null,null);
                    log.info("{}通道{}输出关闭成功,电流为{}A", getDeviceName(), deviceChannel, current);
                    return true;
                }
                catch (DeviceReadException e){
                    log.info("{}通道{}输出关闭失败", getDeviceName(), deviceChannel);
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean setVoltage(Integer deviceChannel, float voltage) {
        if (deviceChannel == null) {
            log.info("{}设置电压:{}V", getDeviceName(), voltage);
        } else {
            log.info("{}通道{}设置电压:{}V", getDeviceName(), deviceChannel, voltage);
        }
        if (isSimulated()) {
            return true;
        }
        return getPowerProtocol() != null && getPowerProtocol().setVoltage(deviceChannel, voltage);
    }

    @Override
    public boolean setVoltageWithUnit(Integer deviceChannel, String voltageWithUnit) throws
            OperationFailNotification {

        float voltage;
        if (NumberUtil.isNumber(voltageWithUnit)) {
            log.info("设置电压:{}V", voltageWithUnit);
            voltage = BaseRegexRule.getVoltage(voltageWithUnit);
        } else if (voltageWithUnit.contains(RANDOM_VALUE_SEPARATOR)) {
            String[] segments = voltageWithUnit.split(RANDOM_VALUE_SEPARATOR);
            if (segments.length != 2) {
                throw new OperationFailNotification(String.format("随机电压指令格式失败:%s", voltageWithUnit));
            }
            String lowerVoltageUnit = segments[0];
            String upperVoltageUnit = segments[1];
            boolean isFloat = lowerVoltageUnit.contains(".") || upperVoltageUnit.contains(".");
            Float lowerVoltages = BaseRegexRule.getVoltage(lowerVoltageUnit);
            Float upperVoltages = BaseRegexRule.getVoltage(upperVoltageUnit);
            if (lowerVoltages == null || upperVoltages == null) {
                throw new OperationFailNotification(String.format("随机电压指令格式失败:%s", voltageWithUnit));
            }
            log.info("设置随机电压:{}~{}", lowerVoltageUnit, upperVoltageUnit);
            if (isFloat) {
                voltage = (float) RandomUtil.randomDouble(lowerVoltages, upperVoltages);
            } else {
                voltage = RandomUtil.randomInt(lowerVoltages.intValue(), upperVoltages.intValue() + 1);
            }
        } else {
            throw new OperationFailNotification(String.format("随机电压指令格式失败:%s", voltageWithUnit));
        }
        if (deviceChannel == null) {
            log.info("{}设置电压:{}V", getDeviceName(), voltage);
        } else {
            log.info("{}通道{}设置电压:{}V", getDeviceName(), deviceChannel, voltage);
        }
        return getPowerProtocol().setVoltage(deviceChannel, voltage);
    }

    public boolean setCurrent(Integer deviceChannel, String current) {
        return setCurrent(deviceChannel, Float.parseFloat(current));
    }

    @Override
    public boolean setCurrent(Integer deviceChannel, float current) {
        if (deviceChannel == null) {
            log.info("{}设置电流{}A", getDeviceName(), current);
        } else {
            log.info("{}通道{}设置电流{}A", getDeviceName(), deviceChannel, current);
        }
        if (isSimulated()) {
            return true;
        }
        return getPowerProtocol().setCurrent(deviceChannel, current);
    }

    public PowerData getPowerData(MeasureIndicator measureIndicator) {
        if (isSimulated()) {
            BundlePowerData powerData = new BundlePowerData();
            if (measureIndicator.equals(MeasureIndicator.VOLTAGE)) {
                powerData.setVoltage((float) RandomUtil.randomDouble(0, 10));
            } else if (measureIndicator.equals(MeasureIndicator.CURRENT)) {
                powerData.setCurrent((float) RandomUtil.randomDouble(0, 10));
            }
            return powerData;
        }
        if (commProtocol.equalsIgnoreCase(CommProtocol.USB)) {
            if (instrument != null) {
                try {
                    BundlePowerData powerData = new BundlePowerData();
                    String value = instrument.readString();
                    //增加空值校验
                    if (value == null || value.trim().isEmpty()) {
                        log.warn("仪器返回空数据");
                        return null;
                    }
                    // 增加数值解析保护
                    try {
                        if (measureIndicator.equals(MeasureIndicator.VOLTAGE)) {
                            powerData.setVoltage(Float.parseFloat(value));
                        } else if (measureIndicator.equals(MeasureIndicator.CURRENT)) {
                            powerData.setCurrent(Float.parseFloat(value));
                        }
                    } catch (NumberFormatException e) {
                        log.error("数据格式异常: {}", value);
                        return BundlePowerData.invalid();
                    }
                    return powerData;
                } catch (JVisaException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        if (serialPortDataListener != null) {
            return ((PowerPortEventListener) serialPortDataListener).getPowerData();
        }
        return BundlePowerData.invalid();
    }

    @Override
    public float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        getPowerProtocol().readVoltage(deviceChannel);
        PowerData powerData = getPowerData(MeasureIndicator.VOLTAGE);
//        log.info("{}获取电压:{}V", getDeviceName(), voltage);
        if (powerData == null) {
            throw new DeviceReadException("电源电压数据获取失败");
        }
        if (powerData instanceof BundlePowerData) {
            Float voltage = ((BundlePowerData) powerData).getVoltage();
            if (voltage == null) {
                throw new DeviceReadException(String.format("%s采集电压为空", getDeviceName()));
            }
            return voltage;
        } else if (powerData instanceof ChannelPowerData) {
            Float voltage = ((ChannelPowerData) powerData).getDataCH1();
            if (voltage == null) {
                throw new DeviceReadException(String.format("%s采集电压为空", getDeviceName()));
            }
            return voltage;
        } else {
            throw new DeviceReadException("程控电源电压获取失败");
        }
    }

    @Override
    public float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        if (isSimulated()) {
            return (float) RandomUtil.randomDouble(1, 10);
        }
        //FIXME：以下两步为异步获取
        getPowerProtocol().readCurrent(deviceChannel);
        PowerData powerData = getPowerData(MeasureIndicator.CURRENT);
        if (powerData == null) {
            powerData = getPowerDataWithRetry(deviceChannel);
        }
//        log.info("{}获取电流:{}A", getDeviceName(), current);
        if (powerData == null) {
            throw new DeviceReadException("电源电流数据获取失败");
        }
        if (powerData instanceof BundlePowerData) {
            return ((BundlePowerData) powerData).getCurrent();
        } else if (powerData instanceof ChannelPowerData) {
            return ((ChannelPowerData) powerData).getDataCH1();
        } else {
            throw new DeviceReadException("电流获取失败");
        }
    }

    private PowerData getPowerDataWithRetry(Integer deviceChannel) {
        // 重试参数设置
        int maxRetries = 3;
        long timeoutMillis = 10_000; // 10秒超时
        long retryInterval = 100; // 重试间隔100ms
        int retryCount = 0;
        PowerData powerData = null;
        long startTime = System.currentTimeMillis();
        while (retryCount < maxRetries &&
                (System.currentTimeMillis() - startTime) < timeoutMillis) {
            try {
                getPowerProtocol().readCurrent(deviceChannel);
                powerData = getPowerData(MeasureIndicator.CURRENT);

                if (powerData != null) {
                    break; // 成功获取数据，跳出循环
                }

                log.warn("电流数据获取为空，第{}次重试...", retryCount + 1);
                retryCount++;

                // 等待间隔
                Thread.sleep(retryInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重置中断状态
                log.error("电流读取重试过程被中断", e);
            }
        }
        return powerData;
    }

    @Deprecated
    public BooleanComparator compareByCondition(String condition, float value, String textPattern) {
        BooleanComparator comparator = compare(condition, value, textPattern);
        log.info("{}比较{}的结果:{}", getDeviceName(), condition, comparator);
        return comparator;
    }

    @Override
    public CriticalVoltage searchMinimumCriticalVoltage(Integer deviceChannel, CriticalVoltage criticalVoltage) throws
            DeviceReadException {
        int timeout = 600 * 1000; //超时600s
        float minimumGap = 0.1f;
        float expectedFluctuatedPowerPercent = 0.5f; //期望功率波动
        int errorTimes = 0;
        long startMills = System.currentTimeMillis();
        CriticalVoltage tempCriticalVoltage = new CriticalVoltage();
        float lastCurrent = fetchCurrent(deviceChannel); //上次电流
        float lastPower = lastCurrent * fetchVoltage(deviceChannel);
        float currentAfterAdjust;
        float upperMinimumVoltage = criticalVoltage.getMinimumUpperVoltage();
        float lowerMinimumVoltage = criticalVoltage.getMinimumLowerVoltage();

        float voltageAdjusted = upperMinimumVoltage;
        boolean upperFound = false;
        testTerminated = false;
        while (!testTerminated) {
            if (System.currentTimeMillis() - startMills > timeout) {
                //超时
                break;
            }
            //搜索最小临界电压
            setVoltage(deviceChannel, voltageAdjusted);
            log.info("自动调节电压:{}V", voltageAdjusted);
            try {
                TimeUnit.MILLISECONDS.sleep(upperFound ? 2000 : 1500);
            } catch (InterruptedException e) {
                break;
            }
            try {
                //等待1s后判断
                currentAfterAdjust = fetchCurrent(deviceChannel);
                log.info("当前电流:{}A", currentAfterAdjust);
                float fluctuatedPowerPercent = Math.abs(lastPower - currentAfterAdjust * voltageAdjusted) / lastPower;
                boolean powerFluctuated = fluctuatedPowerPercent > expectedFluctuatedPowerPercent;
                float currentPower = currentAfterAdjust * voltageAdjusted;
                log.info("上次功率:{}W, 当前功率:{}W，波动{}比例:{}%", lastPower, currentPower,
                        lastPower > currentPower ? "下降" : "上升", fluctuatedPowerPercent * 100);
                if (!upperFound) {
                    //上限没找到
                    if (lastPower > currentPower && powerFluctuated) {
                        //下降趋势&&功率波动超过期望值
                        //临界关机电压
                        tempCriticalVoltage.setMinimumLowerVoltage(voltageAdjusted);
                        //继续搜索最小下限
                        if (lowerMinimumVoltage <= voltageAdjusted) {
                            voltageAdjusted = lowerMinimumVoltage;
                        }
                        upperFound = true;
                        log.info("上限临界电压已找到，现在查找下限临界电压");
                    }
                    voltageAdjusted -= minimumGap;
                } else {
                    //上限找到->现在找下限
                    if (lastPower < currentPower && powerFluctuated) {
                        //上升趋势&&功率波动超过期望值
                        //临界开机电压
                        tempCriticalVoltage.setMinimumUpperVoltage(voltageAdjusted);
                        break;
                    }
                    //从最小下限开始往上搜索
                    voltageAdjusted += minimumGap;
                }
                lastPower = currentPower;
            } catch (DeviceReadException e) {
                errorTimes++;
                if (errorTimes > 5) {
                    //重试次数超过
                    throw e;
                }
            }
        }
        log.info("自动搜索临界电压结果:{}", tempCriticalVoltage);
        return tempCriticalVoltage;
    }

    @Override
    public void testTerminated() {
        testTerminated = true;
    }

}
