package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.constants.AdbConstants;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.utils.NetworkUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Android设备
 */
@Slf4j
public abstract class AndroidDevice extends Device implements IAndroid {

    @Getter
    private final String deviceType = DeviceType.DEVICE_ANDROID;
    private final RestTemplate restTemplateClient = new RestTemplate();
    private Process videoStreamProcess; // 用于管理视频流进程

    public AndroidDevice() {
        this(new DeviceOperationParameter());
    }

    public AndroidDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        GrabRequest grabRequest = new GrabRequest();
        grabRequest.setDeviceUUID(getDeviceUniqueCode());
        grabRequest.setDeviceName(getDeviceName());
        grabRequest.setDeviceModel(getDeviceModel());
//        grabRequest.setDevicePort(getDevicePort());
        DeviceOperationParameter operationParameter = getDeviceOperationParameter();
        try {
            grabRequest.setWidth(operationParameter.getWidth());
            grabRequest.setHeight(operationParameter.getHeight());
        } catch (OperationParameterExtractException e) {
            log.warn("获取Android分辨率参数失败:{}", e.getMessage());
        }
        ParameterizedTypeReference<ResultEntity<Object>> typeRef = new ParameterizedTypeReference<ResultEntity<Object>>() {
        };
        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/grab", NetworkUtils.getServerPort());
        ResultEntity<Object> entity = restTemplateClient.exchange(url, HttpMethod.POST, new HttpEntity<>(grabRequest), typeRef).getBody();
//        throw new OperationFailException();
        if (entity == null) {
            throw new DeviceOpenException(String.format("Post请求失败:%s", url));
        }
        if (!entity.isOk()) {
            throw new DeviceOpenException(entity.getMessage());
        }
        return entity.isOk();
    }


    public static List<AdbStatus> getAllAndroids() {
        List<AdbStatus> adbStatusList = new ArrayList<>();
        try {
            List<String> adbDevicesResult = CommandUtils.executeCommandToArray(AdbConstants.Command.QUERY_ADB_DEVICES);
            log.info("adbDevicesResult:{}", adbDevicesResult);
            if (!adbDevicesResult.isEmpty()) {
                adbDevicesResult.remove(0);
            }
            for (String status : adbDevicesResult) {
                String[] statusArray = status.split("\t");
                AdbStatus adbStatus = new AdbStatus();
                adbStatus.setSerialNumber(statusArray[0]);
                adbStatus.setStatus(statusArray[1]);
                adbStatusList.add(adbStatus);
            }
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        return adbStatusList;
    }

    public String wrapAdbCommand(String command) {
        return command.replaceAll("adb", String.format("adb -s %s", getDeviceName()));
    }

    public synchronized InputStream startVideoStream() throws IOException {
        return startVideoStream(0, 0, 0);
    }

    /**
     * 启动 Android 设备的视频流。
     *
     * @param width   视频宽度。如果为0或负数，则使用设备默认宽度。
     * @param height  视频高度。如果为0或负数，则使用设备默认高度。
     * @param bitRate 视频比特率 (例如 4000000 表示 4Mbps)。如果为0或负数，则使用默认比特率。
     * @return 视频流的 InputStream。
     * @throws IOException 如果执行 ADB 命令失败。
     */
    public synchronized InputStream startVideoStream(int width, int height, int bitRate) throws IOException {
        if (isVideoStreamRunning()) {
            log.info("视频流已在运行，将先停止现有视频流。");
            stopVideoStream();
        }

        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        List<String> commandParts = new ArrayList<>();
        commandParts.add("adb"); // 直接使用 "adb"
        commandParts.add("-s");
        commandParts.add(deviceSerial);
        commandParts.add("exec-out");
        commandParts.add("screenrecord");
        commandParts.add("--output-format=h264");

        if (width > 0 && height > 0) {
            commandParts.add("--size");
            commandParts.add(width + "x" + height);
        }
        if (bitRate > 0) {
            commandParts.add("--bit-rate");
            commandParts.add(String.valueOf(bitRate));
        }
        commandParts.add("-"); // 输出到 stdout

        ProcessBuilder processBuilder = new ProcessBuilder(commandParts);
        log.info("执行视频流命令: {}", String.join(" ", commandParts));
        try {
            videoStreamProcess = processBuilder.start();
            // 检查进程是否成功启动 (可选, 但有助于调试)
            if (!videoStreamProcess.isAlive()) {
                // 读取失败
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(videoStreamProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                videoStreamProcess = null; // 重置，因为启动失败
                throw new IOException("启动视频流进程失败。错误信息: " + errorOutput.toString().trim());
            }
            log.info("视频流已成功启动。设备: {}", deviceSerial);
            return videoStreamProcess.getInputStream();
        } catch (IOException e) {
            log.error("启动视频流失败。设备: {}, 命令: {}. 错误: {}", deviceSerial, String.join(" ", commandParts), e.getMessage());
            videoStreamProcess = null; // 确保在失败时重置
            throw e;
        }
    }

    /**
     * 停止 Android 设备的视频流。
     */
    public synchronized void stopVideoStream() {
        if (videoStreamProcess != null && videoStreamProcess.isAlive()) {
            log.info("正在停止视频流... 设备: {}", getDeviceName());
            videoStreamProcess.destroyForcibly();
            try {
                if (!videoStreamProcess.waitFor(5, TimeUnit.SECONDS)) {
                    log.warn("视频流进程在5秒内未终止。设备: {}", getDeviceName());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待视频流进程终止时被中断。设备: {}", getDeviceName(), e);
            }
            log.info("视频流已停止。设备: {}", getDeviceName());
        }
        videoStreamProcess = null; // 清理引用
    }

    /**
     * 检查视频流是否正在运行。
     *
     * @return 如果视频流进程存在且活动，则返回 true。
     */
    public synchronized boolean isVideoStreamRunning() {
        return videoStreamProcess != null && videoStreamProcess.isAlive();
    }

    /**
     * 获取当前 Android 设备 UI 层级结构的 XML 字符串。
     *
     * @return UI 层级结构的 XML 字符串。
     * @throws IOException 如果执行 ADB 命令失败或读取输出时发生错误。
     */
    public String getUIHierarchyXml() throws IOException {
        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        // 清理旧的 dump 文件 (可选, 但推荐)
        try {
            Process clearProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "rm", "/sdcard/window_dump.xml").start();
            if (!clearProcess.waitFor(5, TimeUnit.SECONDS)) {
                clearProcess.destroyForcibly();
                log.warn("清理 /sdcard/window_dump.xml 超时。");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("清理 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            // 忽略清理错误，可能文件不存在
            log.debug("清理 /sdcard/window_dump.xml 时出错 (可能文件不存在): {}", e.getMessage());
        }

        // 执行 uiautomator dump
        Process dumpProcess = null;
        try {
            dumpProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "uiautomator", "dump", "/sdcard/window_dump.xml").start();
            if (!dumpProcess.waitFor(15, TimeUnit.SECONDS)) { // UI dump 可能需要一些时间
                dumpProcess.destroyForcibly();
                throw new IOException("执行 uiautomator dump 超时。");
            }
            int exitCode = dumpProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(dumpProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("执行 uiautomator dump 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            dumpProcess.destroyForcibly();
            throw new IOException("执行 uiautomator dump 时被中断。", e);
        } catch (IOException e) {
            if (dumpProcess != null) {
                dumpProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }


        // 读取 dump 的 XML 文件内容
        Process catProcess = null;
        StringBuilder xmlContent = new StringBuilder();
        try {
            catProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "cat", "/sdcard/window_dump.xml").start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    xmlContent.append(line).append(System.lineSeparator());
                }
            }

            if (!catProcess.waitFor(10, TimeUnit.SECONDS)) {
                catProcess.destroyForcibly();
                throw new IOException("读取 /sdcard/window_dump.xml 超时。");
            }
            int exitCode = catProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("读取 /sdcard/window_dump.xml 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            if (catProcess != null) {
                catProcess.destroyForcibly();
            }
            throw new IOException("读取 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            if (catProcess != null) {
                catProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }

        if (xmlContent.length() == 0) {
            log.warn("获取到的 UI 层级 XML 内容为空。设备: {}", deviceSerial);
            // 可以选择抛出异常或返回空字符串，具体取决于业务需求
            // throw new IOException("获取到的 UI 层级 XML 内容为空。");
        }

        return xmlContent.toString().trim();
    }

    @Override
    public RectSize getSize() {
        return null;
    }

    @Override
    public Frame grabFrame() {
        // 返回一个空的Frame，实际的视频捕获通过startVideoStream方法实现
        return new Frame();
    }

    /**
     * 通过 adb shell wm size 获取设备屏幕分辨率
     */
    private static int[] getDeviceScreenSize(UsbAndroid usbAndroid) throws IOException {
        String deviceSerial = usbAndroid.getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        List<String> commandParts = new ArrayList<>();
        commandParts.add("adb");
        commandParts.add("-s");
        commandParts.add(deviceSerial);
        commandParts.add("shell");
        commandParts.add("wm");
        commandParts.add("size");

        ProcessBuilder processBuilder = new ProcessBuilder(commandParts);
        log.info("执行获取屏幕分辨率命令: {}", String.join(" ", commandParts));

        try {
            Process process = processBuilder.start();
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append(System.lineSeparator());
                }
            }

            if (!process.waitFor(10, TimeUnit.SECONDS)) {
                process.destroyForcibly();
                throw new IOException("获取屏幕分辨率超时。");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("获取屏幕分辨率失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }

            // 解析输出，格式通常为 "Physical size: 1080x2340" 或 "Override size: 1080x2340"
            String outputStr = output.toString().trim();
            log.info("wm size 输出: {}", outputStr);

            // 查找包含分辨率信息的行
            String[] lines = outputStr.split(System.lineSeparator());
            for (String line : lines) {
                if (line.contains("x") && (line.contains("Physical size:") || line.contains("Override size:"))) {
                    // 提取分辨率部分，例如从 "Physical size: 1080x2340" 中提取 "1080x2340"
                    String resolution = line.substring(line.indexOf(":") + 1).trim();
                    String[] parts = resolution.split("x");
                    if (parts.length == 2) {
                        try {
                            int width = Integer.parseInt(parts[0].trim());
                            int height = Integer.parseInt(parts[1].trim());
                            return new int[]{width, height};
                        } catch (NumberFormatException e) {
                            log.warn("解析分辨率失败: {}", resolution);
                        }
                    }
                }
            }

            // 如果没有找到标准格式，尝试直接查找 "数字x数字" 模式
            for (String line : lines) {
                if (line.matches(".*\\d+x\\d+.*")) {
                    String[] parts = line.replaceAll("[^\\d+x\\d+]", "").split("x");
                    if (parts.length == 2) {
                        try {
                            int width = Integer.parseInt(parts[0].trim());
                            int height = Integer.parseInt(parts[1].trim());
                            return new int[]{width, height};
                        } catch (NumberFormatException e) {
                            log.warn("解析分辨率失败: {}", line);
                        }
                    }
                }
            }

            throw new IOException("无法从 wm size 输出中解析屏幕分辨率: " + outputStr);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("获取屏幕分辨率时被中断。", e);
        }
    }

    public static void main(String[] args) throws IOException {
        UsbAndroid usbAndroid = new UsbAndroid();
        usbAndroid.setDeviceName("1552649637009Q4"); // 请替换为实际的设备序列号

        // 创建自定义的视频显示面板
        VideoPanel videoPanel = new VideoPanel();

        // 创建Swing窗口
        JFrame frame = new JFrame("Android设备视频流");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setLocationRelativeTo(null);
        frame.add(videoPanel);
        frame.setVisible(true);

        // 添加窗口关闭监听器来清理资源
        frame.addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent windowEvent) {
                usbAndroid.stopVideoStream();
            }
        });

        // 在后台线程中启动视频流处理
        new Thread(() -> {
            FFmpegFrameGrabber grabber = null;
            boolean useDirectCommand = false; // 使用InputStream方式，因为直接命令方式不支持复杂命令
            try {
                // 显示正在获取分辨率的状态
                videoPanel.showStatus("正在获取设备屏幕分辨率...");

                // 通过 wm size 获取设备实际屏幕分辨率
                int[] screenSize = getDeviceScreenSize(usbAndroid);
                int width = screenSize[0];
                int height = screenSize[1];
                log.info("设备屏幕分辨率: {}x{}", width, height);

                // 显示正在启动视频流的状态
                videoPanel.showStatus("正在启动视频流 (" + width + "x" + height + ")...");

                // 尝试两种方法：1. 直接命令 2. InputStream

                // 方法2：使用InputStream方式（优化版本）
                InputStream videoStream = usbAndroid.startVideoStream(width, height, 4000000);
                log.info("视频输入流已获取: {}", videoStream);

                // 显示正在初始化解码器的状态
                videoPanel.showStatus("正在初始化视频解码器（InputStream优化模式）...");

                // 关键优化：使用maximumSize=0禁用seek，大幅减少启动时间
                grabber = new FFmpegFrameGrabber(videoStream, 0);
                log.info("使用FFmpegFrameGrabber(InputStream, 0)构造函数，禁用seek以加快启动");
                grabber.setFormat("h264");

                // 针对实时流的极限优化参数设置
                grabber.setOption("probesize", "4096"); // 进一步减少探测大小
                grabber.setOption("analyzeduration", "0"); // 完全跳过分析阶段
                grabber.setOption("fflags", "nobuffer+fastseek+flush_packets"); // 多种无缓冲标志
                grabber.setOption("flags", "low_delay"); // 低延迟模式
                grabber.setOption("stimeout", "3000000"); // 3秒读取超时
                grabber.setOption("max_delay", "0"); // 最小延迟
                grabber.setOption("thread_type", "slice"); // 使用切片线程提高性能

                // 明确设置视频参数，避免自动检测
                grabber.setImageWidth(width);
                grabber.setImageHeight(height);
                grabber.setFrameRate(30);
                grabber.setPixelFormat(0); // AV_PIX_FMT_YUV420P = 0

                log.info("准备启动 FFmpegFrameGrabber（使用实时流优化参数）...");

                // 在单独的线程中启动grabber，避免长时间阻塞
                final FFmpegFrameGrabber finalGrabber = grabber;
                Thread grabberInitThread = new Thread(() -> {
                    try {
                        // 使用 start(false) 跳过流信息查找，大幅加快启动速度
                        finalGrabber.start(false);
                        log.info("FFmpegFrameGrabber 已启动，准备进入视频帧抓取循环。");

                        // 显示正在连接视频流的状态
                        videoPanel.showStatus("视频解码器已启动，正在等待视频帧...");

                        // 创建帧转换器
                        Java2DFrameConverter converter = new Java2DFrameConverter();
                        Frame videoFrame;
                        int frameCount = 0;
                        long lastFrameTime = System.currentTimeMillis();

                        while ((videoFrame = finalGrabber.grab()) != null) {
                            if (frameCount == 0) {
                                log.info("成功抓取并解码第一帧！");
                                videoPanel.showStatus("视频流连接成功！");
                            }
                            frameCount++;
                            BufferedImage image = converter.getBufferedImage(videoFrame);
                            if (image != null) {
                                // 在EDT线程中更新UI
                                videoPanel.updateImage(image);
                                lastFrameTime = System.currentTimeMillis();
                            } else if (frameCount == 1) {
                                log.warn("第一帧已抓取，但转换后的图像为null。");
                            }

                            // 检查是否长时间没有收到帧
                            if (System.currentTimeMillis() - lastFrameTime > 5000) {
                                log.warn("超过5秒没有收到新的视频帧");
                                videoPanel.showError("视频流中断：超过5秒没有收到新帧");
                                break;
                            }
                        }
                        log.warn("视频流结束或 grabber.grab() 返回null。共处理 " + frameCount + " 帧。");

                    } catch (Exception e) {
                        log.error("FFmpegFrameGrabber 启动或抓取失败: {}", e.getMessage(), e);
                        videoPanel.showError("解码器错误: " + e.getMessage());
                    }
                });

                grabberInitThread.setDaemon(true);
                grabberInitThread.start();

                // 等待最多30秒让解码器初始化
                grabberInitThread.join(60000);
                if (grabberInitThread.isAlive()) {
                    log.warn("解码器初始化超时，尝试强制中断");
                    grabberInitThread.interrupt();
                    videoPanel.showError("解码器初始化超时（30秒），请检查设备连接");
                }

            } catch (Exception e) {
                log.error("视频流处理或启动失败: {}", e.getMessage(), e);
                videoPanel.showError("启动错误: " + e.getMessage());
            } finally {
                if (grabber != null) {
                    try {
                        grabber.stop();
                        grabber.release();
                        log.info("FFmpegFrameGrabber 已停止。");
                    } catch (Exception e) {
                        log.error("清理 grabber 资源时出错: {}", e.getMessage(), e);
                    }
                }
                // 只有在使用InputStream模式时才需要手动停止视频流
                if (!useDirectCommand) {
                    usbAndroid.stopVideoStream();
                }
            }
        }).start();
    }
}

/**
 * 用于显示视频的自定义JPanel。
 * 这种方法比使用Canvas.getGraphics()更稳定。
 */
class VideoPanel extends JPanel {
    private BufferedImage image;
    private String errorMessage;
    private String statusMessage;

    public void updateImage(BufferedImage image) {
        this.image = image;
        this.errorMessage = null; // 清除之前的错误信息
        this.statusMessage = null; // 清除状态信息
        SwingUtilities.invokeLater(this::repaint); // 确保在EDT上重绘
    }

    public void showError(String message) {
        this.errorMessage = message;
        this.statusMessage = null; // 清除状态信息
        this.image = null; // 清除图像
        SwingUtilities.invokeLater(this::repaint);
    }

    public void showStatus(String message) {
        this.statusMessage = message;
        this.errorMessage = null; // 清除错误信息
        SwingUtilities.invokeLater(this::repaint);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g;

        // 绘制黑色背景
        g2d.setColor(Color.BLACK);
        g2d.fillRect(0, 0, getWidth(), getHeight());

        if (image != null) {
            // 绘制视频帧，缩放以适应面板大小
            g2d.drawImage(image, 0, 0, getWidth(), getHeight(), null);
        } else if (errorMessage != null) {
            // 显示错误信息
            g2d.setColor(Color.RED);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            drawMultilineText(g2d, errorMessage, 10, 30);
        } else if (statusMessage != null) {
            // 显示状态信息
            g2d.setColor(Color.WHITE);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            drawMultilineText(g2d, statusMessage, 10, 30);
        } else {
            // 默认等待信息
            g2d.setColor(Color.WHITE);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            g2d.drawString("正在初始化...", 10, 30);
        }
    }

    private void drawMultilineText(Graphics2D g2d, String text, int x, int y) {
        String[] lines = text.split("\n");
        FontMetrics fm = g2d.getFontMetrics();
        int lineHeight = fm.getHeight();
        for (int i = 0; i < lines.length; i++) {
            g2d.drawString(lines[i], x, y + i * lineHeight);
        }
    }
}
