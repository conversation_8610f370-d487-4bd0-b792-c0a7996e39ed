package com.desaysv.workserver.devices.device_udp.test;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

public class UDPServer {
    public static void main(String[] args) {
        try {
            // 1. 创建DatagramSocket并绑定8888端口
            DatagramSocket serverSocket = new DatagramSocket(9999);
            System.out.println("服务器已启动，等待客户端消息...");

            // 2. 创建接收缓冲区
            byte[] receiveData = new byte[1024];

            while (true) {
                // 3. 创建接收数据包
                DatagramPacket receivePacket = 
                    new DatagramPacket(receiveData, receiveData.length);
                
                // 4. 阻塞等待接收数据
                serverSocket.receive(receivePacket);
                
                // 5. 解析接收数据
                String clientMessage = new String(
                    receivePacket.getData(),
                    0,
                    receivePacket.getLength(),
                    "UTF-8"
                );
                System.out.println("收到客户端消息：" + clientMessage);

                // 6. 获取客户端地址和端口
                InetAddress clientAddress = receivePacket.getAddress();
                int clientPort = receivePacket.getPort();

                // 7. 准备回复数据
                String response = "服务器已收到你的消息：" + clientMessage;
                byte[] sendData = response.getBytes("UTF-8");

                // 8. 创建发送数据包
                DatagramPacket sendPacket = new DatagramPacket(
                    sendData,
                    sendData.length,
                    clientAddress,
                    clientPort
                );

                // 9. 发送回复
                serverSocket.send(sendPacket);
                System.out.println("已发送回复至客户端");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}